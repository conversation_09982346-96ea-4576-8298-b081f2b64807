import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import io
import os
import tempfile
import zipfile
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# AutoGluon imports
try:
    from autogluon.tabular import TabularDataset, TabularPredictor
    from autogluon.multimodal import MultiModalPredictor
    from autogluon.timeseries import TimeSeriesDataset, TimeSeriesPredictor
    from autogluon.features import FeatureGenerator
    from autogluon.core.utils.utils import setup_outputdir
    from autogluon.core.metrics import make_scorer
    AUTOGLUON_AVAILABLE = True
except ImportError:
    AUTOGLUON_AVAILABLE = False
    st.error("AutoGluon not installed. Please install with: pip install autogluon==1.3.1")

# Page configuration
st.set_page_config(
    page_title="No Code ML Platform",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .stTabs [data-baseweb="tab-list"] {
        gap: 2px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'predictor' not in st.session_state:
    st.session_state.predictor = None
if 'data' not in st.session_state:
    st.session_state.data = None
if 'model_trained' not in st.session_state:
    st.session_state.model_trained = False
if 'predictions' not in st.session_state:
    st.session_state.predictions = None

# Sample datasets generator
def generate_sample_datasets():
    """Generate various sample datasets for different ML tasks"""
    
    # Classification dataset - Customer Churn
    np.random.seed(42)
    n_samples = 1000
    
    churn_data = {
        'customer_id': range(1, n_samples + 1),
        'age': np.random.normal(45, 15, n_samples).astype(int),
        'tenure': np.random.normal(32, 20, n_samples).astype(int),
        'monthly_charges': np.random.normal(65, 25, n_samples),
        'total_charges': np.random.normal(2000, 1500, n_samples),
        'contract_type': np.random.choice(['Month-to-month', 'One year', 'Two year'], n_samples),
        'payment_method': np.random.choice(['Electronic check', 'Mailed check', 'Bank transfer', 'Credit card'], n_samples),
        'internet_service': np.random.choice(['DSL', 'Fiber optic', 'No'], n_samples),
        'online_security': np.random.choice(['Yes', 'No', 'No internet service'], n_samples),
        'tech_support': np.random.choice(['Yes', 'No', 'No internet service'], n_samples),
    }
    
    # Create churn target based on some logic
    churn_prob = (
        (churn_data['monthly_charges'] > 70) * 0.3 +
        (churn_data['tenure'] < 12) * 0.4 +
        (np.array(churn_data['contract_type']) == 'Month-to-month') * 0.2 +
        np.random.random(n_samples) * 0.1
    )
    churn_data['churn'] = (churn_prob > 0.5).astype(int)
    
    churn_df = pd.DataFrame(churn_data)
    
    # Regression dataset - House Prices
    np.random.seed(123)
    n_houses = 800
    
    house_data = {
        'bedrooms': np.random.choice([1, 2, 3, 4, 5], n_houses, p=[0.1, 0.2, 0.4, 0.25, 0.05]),
        'bathrooms': np.random.choice([1, 1.5, 2, 2.5, 3, 3.5], n_houses),
        'sqft_living': np.random.normal(2000, 800, n_houses),
        'sqft_lot': np.random.normal(7500, 5000, n_houses),
        'floors': np.random.choice([1, 1.5, 2, 2.5, 3], n_houses),
        'waterfront': np.random.choice([0, 1], n_houses, p=[0.9, 0.1]),
        'view': np.random.choice([0, 1, 2, 3, 4], n_houses),
        'condition': np.random.choice([1, 2, 3, 4, 5], n_houses),
        'grade': np.random.choice(range(1, 14), n_houses),
        'yr_built': np.random.choice(range(1900, 2023), n_houses),
        'zipcode': np.random.choice(range(98000, 98200), n_houses),
    }
    
    # Create price based on features
    price = (
        house_data['bedrooms'] * 20000 +
        house_data['bathrooms'] * 15000 +
        house_data['sqft_living'] * 150 +
        house_data['sqft_lot'] * 10 +
        house_data['waterfront'] * 200000 +
        house_data['view'] * 50000 +
        house_data['condition'] * 30000 +
        house_data['grade'] * 25000 +
        np.random.normal(0, 50000, n_houses)
    )
    house_data['price'] = np.maximum(price, 50000)  # Minimum price
    
    house_df = pd.DataFrame(house_data)
    
    # Time series dataset - Sales Data
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='D')
    n_days = len(dates)
    
    # Create seasonal pattern
    seasonal_pattern = 10 * np.sin(2 * np.pi * np.arange(n_days) / 365.25)
    trend = 0.01 * np.arange(n_days)
    noise = np.random.normal(0, 5, n_days)
    
    sales_data = {
        'timestamp': dates,
        'item_id': 'product_A',
        'target': 100 + seasonal_pattern + trend + noise + np.random.exponential(20, n_days)
    }
    
    sales_df = pd.DataFrame(sales_data)
    
    # Multimodal dataset - Product Reviews (text + numerical)
    np.random.seed(456)
    n_reviews = 500
    
    products = ['Laptop', 'Phone', 'Tablet', 'Watch', 'Headphones']
    positive_words = ['excellent', 'amazing', 'great', 'fantastic', 'wonderful', 'perfect', 'love']
    negative_words = ['terrible', 'awful', 'hate', 'disappointing', 'poor', 'bad', 'worst']
    neutral_words = ['okay', 'average', 'decent', 'fine', 'acceptable', 'normal']
    
    reviews_data = {
        'product': np.random.choice(products, n_reviews),
        'price': np.random.uniform(50, 1500, n_reviews),
        'brand_rating': np.random.uniform(1, 5, n_reviews),
        'features_count': np.random.randint(5, 20, n_reviews),
    }
    
    # Generate review text based on rating
    reviews_text = []
    ratings = []
    
    for i in range(n_reviews):
        rating = np.random.choice([1, 2, 3, 4, 5], p=[0.1, 0.1, 0.2, 0.3, 0.3])
        ratings.append(rating)
        
        if rating >= 4:
            words = np.random.choice(positive_words, 3)
        elif rating <= 2:
            words = np.random.choice(negative_words, 3)
        else:
            words = np.random.choice(neutral_words, 3)
        
        review = f"This {reviews_data['product'][i]} is {words[0]} and {words[1]}. {words[2]} experience overall."
        reviews_text.append(review)
    
    reviews_data['review_text'] = reviews_text
    reviews_data['rating'] = ratings
    
    reviews_df = pd.DataFrame(reviews_data)
    
    return {
        'Customer Churn (Classification)': churn_df,
        'House Prices (Regression)': house_df,
        'Sales Forecast (Time Series)': sales_df,
        'Product Reviews (Multimodal)': reviews_df
    }

# Main application
def main():
    st.markdown('<h1 class="main-header">🤖 No Code ML Platform</h1>', unsafe_allow_html=True)
    st.markdown("### Powered by AutoGluon 1.3.1")
    
    if not AUTOGLUON_AVAILABLE:
        st.stop()
    
    # Sidebar
    st.sidebar.title("Navigation")
    
    tabs = ["📊 Data Loading", "🔍 Data Exploration", "⚙️ Model Training", "🎯 Predictions", "📈 Model Analysis", "⚡ AutoML Features"]
    selected_tab = st.sidebar.selectbox("Select Section", tabs)
    
    # Generate sample datasets
    sample_datasets = generate_sample_datasets()
    
    # Data Loading Tab
    if selected_tab == "📊 Data Loading":
        st.markdown('<h2 class="section-header">Data Loading</h2>', unsafe_allow_html=True)
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.subheader("Upload Your Data")
            uploaded_file = st.file_uploader(
                "Choose a CSV file",
                type=['csv'],
                help="Upload your CSV file to get started with ML"
            )
            
            if uploaded_file is not None:
                try:
                    data = pd.read_csv(uploaded_file)
                    st.session_state.data = data
                    st.success(f"Data loaded successfully! Shape: {data.shape}")
                    st.dataframe(data.head())
                except Exception as e:
                    st.error(f"Error loading data: {str(e)}")
        
        with col2:
            st.subheader("Or Use Sample Datasets")
            selected_dataset = st.selectbox(
                "Choose a sample dataset",
                list(sample_datasets.keys())
            )
            
            if st.button("Load Sample Dataset", type="primary"):
                st.session_state.data = sample_datasets[selected_dataset]
                st.success(f"Sample dataset loaded: {selected_dataset}")
                st.dataframe(st.session_state.data.head())
        
        if st.session_state.data is not None:
            st.subheader("Dataset Info")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Rows", len(st.session_state.data))
            with col2:
                st.metric("Columns", len(st.session_state.data.columns))
            with col3:
                st.metric("Memory Usage", f"{st.session_state.data.memory_usage(deep=True).sum() / 1024:.1f} KB")
    
    # Data Exploration Tab
    elif selected_tab == "🔍 Data Exploration":
        st.markdown('<h2 class="section-header">Data Exploration</h2>', unsafe_allow_html=True)
        
        if st.session_state.data is None:
            st.warning("Please load data first!")
            return
        
        data = st.session_state.data
        
        # Dataset overview
        tab1, tab2, tab3, tab4 = st.tabs(["📋 Overview", "📊 Statistics", "🎨 Visualizations", "🔧 Data Quality"])
        
        with tab1:
            st.subheader("Dataset Overview")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**First 10 rows:**")
                st.dataframe(data.head(10))
            
            with col2:
                st.write("**Data Types:**")
                dtypes_df = pd.DataFrame({
                    'Column': data.dtypes.index,
                    'Type': data.dtypes.values
                })
                st.dataframe(dtypes_df)
        
        with tab2:
            st.subheader("Statistical Summary")
            
            # Numerical columns
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                st.write("**Numerical Features:**")
                st.dataframe(data[numeric_cols].describe())
            
            # Categorical columns
            cat_cols = data.select_dtypes(include=['object']).columns
            if len(cat_cols) > 0:
                st.write("**Categorical Features:**")
                for col in cat_cols[:5]:  # Show first 5 categorical columns
                    st.write(f"**{col}:**")
                    value_counts = data[col].value_counts().head(10)
                    st.bar_chart(value_counts)
        
        with tab3:
            st.subheader("Data Visualizations")
            
            if len(numeric_cols) > 0:
                # Distribution plots
                st.write("**Feature Distributions:**")
                selected_numeric = st.selectbox("Select numerical feature", numeric_cols)
                
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
                data[selected_numeric].hist(bins=30, ax=ax1)
                ax1.set_title(f'Histogram of {selected_numeric}')
                data[selected_numeric].plot(kind='box', ax=ax2)
                ax2.set_title(f'Box Plot of {selected_numeric}')
                st.pyplot(fig)
                
                # Correlation heatmap
                if len(numeric_cols) > 1:
                    st.write("**Correlation Matrix:**")
                    corr_matrix = data[numeric_cols].corr()
                    fig, ax = plt.subplots(figsize=(10, 8))
                    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=ax)
                    st.pyplot(fig)
        
        with tab4:
            st.subheader("Data Quality Assessment")
            
            # Missing values
            missing_data = data.isnull().sum()
            missing_data = missing_data[missing_data > 0]
            
            if not missing_data.empty:
                st.write("**Missing Values:**")
                missing_df = pd.DataFrame({
                    'Column': missing_data.index,
                    'Missing Count': missing_data.values,
                    'Missing %': (missing_data.values / len(data) * 100).round(2)
                })
                st.dataframe(missing_df)
                
                # Visualize missing data
                fig, ax = plt.subplots(figsize=(10, 6))
                missing_df.set_index('Column')['Missing %'].plot(kind='bar', ax=ax)
                ax.set_title('Missing Data Percentage by Column')
                ax.set_ylabel('Missing %')
                plt.xticks(rotation=45)
                st.pyplot(fig)
            else:
                st.success("No missing values found!")
            
            # Duplicate rows
            duplicates = data.duplicated().sum()
            st.metric("Duplicate Rows", duplicates)
            
            # Data quality score
            total_cells = data.shape[0] * data.shape[1]
            missing_cells = data.isnull().sum().sum()
            quality_score = ((total_cells - missing_cells) / total_cells) * 100
            st.metric("Data Quality Score", f"{quality_score:.1f}%")
    
    # Model Training Tab
    elif selected_tab == "⚙️ Model Training":
        st.markdown('<h2 class="section-header">Model Training</h2>', unsafe_allow_html=True)
        
        if st.session_state.data is None:
            st.warning("Please load data first!")
            return
        
        data = st.session_state.data
        
        # Model configuration
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.subheader("Model Configuration")
            
            # Task type detection and selection
            target_column = st.selectbox("Select Target Column", data.columns.tolist())
            
            if target_column:
                # Auto-detect task type
                target_series = data[target_column]
                
                if target_series.dtype in ['object', 'category'] or target_series.nunique() < 10:
                    suggested_task = "classification"
                elif 'timestamp' in target_column.lower() or 'date' in target_column.lower():
                    suggested_task = "time_series"
                else:
                    suggested_task = "regression"
                
                task_type = st.selectbox(
                    "Task Type",
                    ["classification", "regression", "time_series", "multimodal"],
                    index=["classification", "regression", "time_series", "multimodal"].index(suggested_task)
                )
                
                # Feature selection
                available_features = [col for col in data.columns if col != target_column]
                selected_features = st.multiselect(
                    "Select Features (leave empty for all)",
                    available_features,
                    default=available_features[:10] if len(available_features) > 10 else available_features
                )
                
                if not selected_features:
                    selected_features = available_features
        
        with col2:
            st.subheader("Training Settings")
            
            # Training parameters
            time_limit = st.slider("Time Limit (seconds)", 60, 3600, 300)
            quality_preset = st.selectbox(
                "Quality Preset",
                ["medium_quality_faster_train", "good_quality_faster_inference", "high_quality_best_quality", "best_quality"]
            )
            
            eval_metric = None
            if task_type == "classification":
                eval_metric = st.selectbox("Evaluation Metric", ["accuracy", "f1", "roc_auc", "log_loss"])
            elif task_type == "regression":
                eval_metric = st.selectbox("Evaluation Metric", ["rmse", "mae", "r2", "mse"])
            
            # Advanced settings
            with st.expander("Advanced Settings"):
                train_split = st.slider("Training Split", 0.6, 0.9, 0.8)
                auto_feature_engineering = st.checkbox("Enable Auto Feature Engineering", True)
                hyperparameter_tune = st.checkbox("Enable Hyperparameter Tuning", True)
                ensemble_models = st.checkbox("Enable Model Ensembling", True)
        
        # Training button and process
        if st.button("🚀 Start Training", type="primary"):
            if target_column and selected_features:
                train_model(data, target_column, selected_features, task_type, time_limit, 
                           quality_preset, eval_metric, train_split)
            else:
                st.error("Please select target column and features!")
        
        # Display training status
        if st.session_state.model_trained and st.session_state.predictor:
            st.success("✅ Model trained successfully!")
            
            # Model summary
            st.subheader("Model Summary")
            try:
                leaderboard = st.session_state.predictor.leaderboard(silent=True)
                st.dataframe(leaderboard)
            except Exception as e:
                st.warning(f"Could not display leaderboard: {str(e)}")
    
    # Predictions Tab
    elif selected_tab == "🎯 Predictions":
        st.markdown('<h2 class="section-header">Model Predictions</h2>', unsafe_allow_html=True)
        
        if not st.session_state.model_trained or st.session_state.predictor is None:
            st.warning("Please train a model first!")
            return
        
        predictor = st.session_state.predictor
        
        tab1, tab2, tab3 = st.tabs(["🔮 Single Prediction", "📊 Batch Predictions", "📁 Upload & Predict"])
        
        with tab1:
            st.subheader("Single Prediction")
            
            # Create input form based on features
            if st.session_state.data is not None:
                data = st.session_state.data
                feature_inputs = {}
                
                col1, col2 = st.columns(2)
                
                for i, column in enumerate(data.columns):
                    if column == predictor.label:  # Skip target column
                        continue
                    
                    with col1 if i % 2 == 0 else col2:
                        if data[column].dtype in ['object', 'category']:
                            unique_values = data[column].unique()
                            feature_inputs[column] = st.selectbox(f"{column}", unique_values)
                        elif data[column].dtype in ['int64', 'int32']:
                            min_val, max_val = int(data[column].min()), int(data[column].max())
                            feature_inputs[column] = st.number_input(f"{column}", min_val, max_val, int(data[column].mean()))
                        else:
                            min_val, max_val = float(data[column].min()), float(data[column].max())
                            feature_inputs[column] = st.number_input(f"{column}", min_val, max_val, float(data[column].mean()))
                
                if st.button("🎯 Make Prediction", type="primary"):
                    try:
                        input_df = pd.DataFrame([feature_inputs])
                        prediction = predictor.predict(input_df)
                        
                        st.success("Prediction made successfully!")
                        st.metric("Predicted Value", prediction.iloc[0])
                        
                        # Show prediction probabilities for classification
                        try:
                            pred_proba = predictor.predict_proba(input_df)
                            if pred_proba is not None:
                                st.subheader("Prediction Probabilities")
                                prob_df = pd.DataFrame(pred_proba).T
                                prob_df.columns = ['Probability']
                                st.dataframe(prob_df)
                        except:
                            pass
                            
                    except Exception as e:
                        st.error(f"Prediction error: {str(e)}")
        
        with tab2:
            st.subheader("Batch Predictions")
            
            if st.session_state.data is not None:
                data = st.session_state.data
                
                # Option to use test data or full dataset
                use_test_data = st.checkbox("Use test split for predictions", True)
                
                if st.button("🔄 Generate Batch Predictions", type="primary"):
                    try:
                        if use_test_data:
                            # Use a portion of data as test set
                            test_data = data.sample(n=min(100, len(data) // 4))
                        else:
                            test_data = data
                        
                        # Remove target column
                        test_features = test_data.drop(columns=[predictor.label])
                        predictions = predictor.predict(test_features)
                        
                        # Create results dataframe
                        results_df = test_features.copy()
                        results_df['Actual'] = test_data[predictor.label].values
                        results_df['Predicted'] = predictions.values
                        
                        st.session_state.predictions = results_df
                        
                        st.success(f"Generated {len(predictions)} predictions!")
                        st.dataframe(results_df.head(20))
                        
                        # Download predictions
                        csv = results_df.to_csv(index=False)
                        st.download_button(
                            "📥 Download Predictions",
                            csv,
                            "predictions.csv",
                            "text/csv"
                        )
                        
                    except Exception as e:
                        st.error(f"Batch prediction error: {str(e)}")
        
        with tab3:
            st.subheader("Upload New Data for Predictions")
            
            uploaded_file = st.file_uploader(
                "Upload CSV file for predictions",
                type=['csv'],
                help="Upload new data to make predictions"
            )
            
            if uploaded_file is not None:
                try:
                    new_data = pd.read_csv(uploaded_file)
                    st.dataframe(new_data.head())
                    
                    if st.button("🎯 Predict on New Data", type="primary"):
                        predictions = predictor.predict(new_data)
                        
                        # Create results
                        results_df = new_data.copy()
                        results_df['Predictions'] = predictions.values
                        
                        st.success("Predictions completed!")
                        st.dataframe(results_df)
                        
                        # Download
                        csv = results_df.to_csv(index=False)
                        st.download_button(
                            "📥 Download Results",
                            csv,
                            "new_data_predictions.csv",
                            "text/csv"
                        )
                        
                except Exception as e:
                    st.error(f"Error processing new data: {str(e)}")
    
    # Model Analysis Tab
    elif selected_tab == "📈 Model Analysis":
        st.markdown('<h2 class="section-header">Model Analysis & Interpretation</h2>', unsafe_allow_html=True)
        
        if not st.session_state.model_trained or st.session_state.predictor is None:
            st.warning("Please train a model first!")
            return
        
        predictor = st.session_state.predictor
        
        tab1, tab2, tab3, tab4 = st.tabs(["🏆 Performance", "📊 Feature Importance", "🎨 Visualizations", "🔍 Model Details"])
        
        with tab1:
            st.subheader("Model Performance")
            
            try:
                # Leaderboard
                leaderboard = predictor.leaderboard(silent=True)
                st.write("**Model Leaderboard:**")
                st.dataframe(leaderboard)
                
                # Best model info
                best_model = leaderboard.iloc[0]['model']
                best_score = leaderboard.iloc[0]['score_val']
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Best Model", best_model)
                with col2:
                    st.metric("Best Score", f"{best_score:.4f}")
                with col3:
                    st.metric("Total Models", len(leaderboard))
                
                # Performance visualization
                fig = px.bar(
                    leaderboard.head(10),
                    x='model',
                    y='score_val',
                    title="Model Performance Comparison"
                )
                fig.update_xaxis(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)
                
            except Exception as e:
                st.error(f"Error displaying performance: {str(e)}")
        
        with tab2:
            st.subheader("Feature Importance")
            
            try:
                importance = predictor.feature_importance(silent=True)
                
                if importance is not None and not importance.empty:
                    st.write("**Feature Importance Scores:**")
                    st.dataframe(importance)
                    
                    # Visualization
                    fig = px.bar(
                        importance.head(15),
                        x='importance',
                        y='feature',
                        orientation='h',
                        title="Top 15 Most Important Features"
                    )
                    fig.update_layout(yaxis={'categoryorder':'total ascending'})
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.warning("Feature importance not available for this model.")
                    
            except Exception as e:
                st.warning(f"Feature importance not available: {str(e)}")
        
        with tab3:
            st.subheader("Model Visualizations")
            
            if st.session_state.predictions is not None:
                predictions_df = st.session_state.predictions
                
                # Actual vs Predicted scatter plot
                if 'Actual' in predictions_df.columns and 'Predicted' in predictions_df.columns:
                    fig = px.scatter(
                        predictions_df,
                        x='Actual',
                        y='Predicted',
                        title="Actual vs Predicted Values",
                        trendline="ols"
                    )
                    fig.add_shape(
                        type="line",
                        x0=predictions_df['Actual'].min(),
                        y0=predictions_df['Actual'].min(),
                        x1=predictions_df['Actual'].max(),
                        y1=predictions_df['Actual'].max(),
                        line=dict(dash="dash", color="red")
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # Residuals plot
                    residuals = predictions_df['Actual'] - predictions_df['Predicted']
                    fig = px.histogram(residuals, title="Residuals Distribution")
                    st.plotly_chart(fig, use_container_width=True)
            
            else:
                st.info("Generate